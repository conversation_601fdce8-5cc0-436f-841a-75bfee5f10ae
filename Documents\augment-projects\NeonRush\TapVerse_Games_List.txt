TAPVERSE GAMES COMPREHENSIVE LIST
==================================

This document contains all games in TapVerse with their mechanics, objectives, controls, and implementation details.

=== CORE LEVEL GAMES (100 Levels) ===

1. TAP TARGET GAME (Levels 1-10)
   Mechanic: Basic tap targets
   Objective: Tap glowing targets to score points and reach the goal score
   Controls: Tap only
   Scoring: Different target types give different points (easy/medium/hard balls)
   Special Features: Red enemies give penalties, adaptive difficulty
   Code Location: lib/games/tap_target_game.dart

2. SWIPE TO AVOID (Levels 11-20)
   Mechanic: Swipe in specific directions
   Objective: Dodge incoming obstacles by swiping to move player
   Controls: Swipe/drag to move player position
   Scoring: Points for successful dodges, combo system
   Special Features: Collision detection, particle effects
   Code Location: lib/games/swipe_avoid_game.dart

3. HOLD THE GLOW (Levels 21-30)
   Mechanic: Hold targets for duration
   Objective: Hold the glowing area to fill progress bar to 100%
   Controls: Tap and hold on glow area
   Scoring: Progress-based scoring with time bonuses
   Special Features: Progress decreases when not holding
   Code Location: lib/games/hold_glow_game.dart

4. AVOID MODE (Levels 31-40)
   Mechanic: Avoid red targets, tap others
   Objective: Tap good targets while avoiding red enemies
   Controls: Tap only (selective tapping)
   Scoring: Points for good targets, penalties for red enemies
   Special Features: Mixed target types, penalty system
   Code Location: Integrated in mechanics_engine.dart

5. MEMORY MODE (Levels 41-50)
   Mechanic: Remember and repeat sequences
   Objective: Watch sequence then repeat it correctly
   Controls: Tap in sequence order
   Scoring: Points for correct sequences, combo bonuses
   Special Features: Sequence length increases with difficulty
   Code Location: Integrated in mechanics_engine.dart

6. REACTION MODE (Levels 51-60)
   Mechanic: Quick reaction to visual cues
   Objective: React quickly to visual prompts and cues
   Controls: Tap when prompted
   Scoring: Speed-based scoring, reaction time bonuses
   Special Features: Timing-critical gameplay
   Code Location: Integrated in mechanics_engine.dart

7. DRAG MODE (Levels 61-70)
   Mechanic: Drag targets to specific areas
   Objective: Drag objects to designated target zones
   Controls: Drag and drop gestures
   Scoring: Accuracy and speed-based scoring
   Special Features: Precision targeting required
   Code Location: Integrated in mechanics_engine.dart

8. ROTATE MODE (Levels 71-80)
   Mechanic: Rotate device or targets
   Objective: Rotate elements to match patterns or orientations
   Controls: Device rotation or touch rotation
   Scoring: Accuracy-based scoring
   Special Features: Orientation-based gameplay
   Code Location: Integrated in mechanics_engine.dart

9. DEFLECT MODE (Levels 81-90)
   Mechanic: Deflect moving targets
   Objective: Deflect incoming projectiles or objects
   Controls: Tap to deflect at right timing
   Scoring: Successful deflections give points
   Special Features: Physics-based deflection mechanics
   Code Location: Integrated in mechanics_engine.dart

10. CHAOS MODE (Levels 91-100)
    Mechanic: Multiple mechanics combined
    Objective: Adapt to rapidly changing game mechanics
    Controls: All control types (tap, swipe, hold, drag, etc.)
    Scoring: Variable scoring based on active mechanic
    Special Features: Mechanics switch every few seconds
    Code Location: Integrated in mechanics_engine.dart

=== BOSS FIGHTS ===

BOSS FIGHT GAME
Levels: 5 (easy), then every 10 levels (10, 20, 30, 40, 50, 60, 70, 80, 90, 100)
Mechanic: Multi-stage boss battles
Objective: Defeat boss by tapping required number of times per stage
Controls: Tap boss (avoid red danger zones)
Scoring: Stage completion bonuses, time bonuses
Special Features: 3 stages per boss, red penalty areas, increasing difficulty
Code Location: lib/games/boss_fight_game.dart

=== BONUS GAMES (15 Total) ===

1. NEON SNAKE
   Mechanic: Classic snake gameplay with neon theme
   Objective: Eat food to grow snake without hitting walls or self
   Controls: Swipe to change direction
   Scoring: 10 points per food eaten
   Special Features: Neon visual effects, grid-based movement
   Code Location: lib/games/neon_snake_game.dart

2. NEON PONG
   Mechanic: Classic pong with AI opponent
   Objective: Score points by getting ball past AI paddle
   Controls: Drag to move player paddle
   Scoring: First to reach target score wins
   Special Features: AI opponent, ball physics, neon effects
   Code Location: lib/games/neon_pong_game.dart

3. ASTRO BLASTER
   Mechanic: Space shooter with enemies and power-ups
   Objective: Destroy enemies while avoiding collisions
   Controls: Drag to move ship, auto-fire bullets
   Scoring: Points for destroyed enemies, wave progression
   Special Features: Multiple enemy types, power-ups, wave system
   Code Location: lib/screens/bonus_games/astro_blaster_screen.dart

4. CRATE SMASH
   Mechanic: Choose one crate to smash for rewards
   Objective: Pick the crate with the highest reward
   Controls: Tap to smash chosen crate
   Scoring: Token rewards based on crate contents
   Special Features: Risk/reward mechanics, one-time choice
   Code Location: lib/games/crate_smash_mini_game.dart

5. GLOW FLOW
   Mechanic: Connect flowing lights through grid paths
   Objective: Create path from start to end node
   Controls: Drag to draw connection paths
   Scoring: Points for successful connections, move efficiency
   Special Features: Grid-based puzzle, obstacles, limited moves
   Code Location: lib/screens/bonus_games/glow_flow_screen.dart

6. NEON STACK
   Mechanic: Stack falling blocks precisely
   Objective: Stack blocks as high as possible with precision
   Controls: Tap to drop moving block
   Scoring: Points per successfully stacked block
   Special Features: Block width decreases with imprecision
   Code Location: lib/games/bonus/neon_stack_game.dart

7. SWIPE SLICE
   Mechanic: Slice flying objects while avoiding bombs
   Objective: Slice fruits/objects, avoid bombs
   Controls: Swipe to slice objects
   Scoring: 10 points per sliced object
   Special Features: Bomb penalties, slice trail effects
   Code Location: lib/games/bonus/swipe_slice_game.dart

8. TAPRUNNER
   Mechanic: Endless runner with jumping
   Objective: Run and jump over obstacles as far as possible
   Controls: Tap to jump
   Scoring: Distance-based scoring, obstacle avoidance
   Special Features: Platform generation, physics-based jumping
   Code Location: lib/games/bonus/tap_runner_game.dart

9. NEON DIG
   Mechanic: Dig through blocks to find treasures
   Objective: Dig deep while avoiding traps, find treasures
   Controls: Tap blocks to destroy them
   Scoring: 10 points normal blocks, 50 treasure, 100 boss, -25 traps
   Special Features: Different block types, depth progression
   Code Location: lib/games/bonus/neon_dig_game.dart

10. SLIDEMATCH
    Mechanic: Match-3 puzzle with sliding tiles
    Objective: Match 3+ tiles by swapping adjacent tiles
    Controls: Tap to select, tap adjacent to swap
    Scoring: Points per match, combo bonuses
    Special Features: Gravity system, combo chains, limited moves
    Code Location: lib/games/bonus/slide_match_game.dart

11. TAPSHOT
    Mechanic: Lane-based shooting gallery
    Objective: Shoot falling targets in 4 lanes
    Controls: Tap lane to shoot bullet upward
    Scoring: Points per target hit, special target bonuses
    Special Features: 4-lane system, bullet-target collision
    Code Location: lib/games/bonus/tap_shot_game.dart

12. DRAG MAZE
    Mechanic: Navigate orb through generated mazes
    Objective: Drag orb from start to end without hitting walls
    Controls: Drag orb through maze paths
    Scoring: 100 points + time bonus for completion
    Special Features: Procedural maze generation, trail effects
    Code Location: lib/games/bonus/drag_maze_game.dart

13. COLORZAP
    Mechanic: Quick color matching reaction game
    Objective: Tap when colors match, avoid when they don't
    Controls: Tap when prompted
    Scoring: 10 points per correct match
    Special Features: Color comparison, reaction timing, lives system
    Code Location: lib/games/bonus/color_zap_game.dart

14. BOUNCETAP
    Mechanic: Keep ball bouncing on platforms
    Objective: Bounce ball on platforms, tap for extra boost
    Controls: Tap to give ball upward boost
    Scoring: Points per platform bounce
    Special Features: Physics-based ball movement, platform collision
    Code Location: lib/games/bonus/bounce_tap_game.dart

15. TAPCRAFT
    Mechanic: Tap to build/craft with progress bars
    Objective: Tap to fill progress bar and complete levels
    Controls: Tap anywhere to increase progress
    Scoring: Progress-based completion
    Special Features: Visual building blocks, tap effects
    Code Location: lib/games/bonus/tap_craft_game.dart

=== GAME MECHANICS SYSTEM ===

The game uses a sophisticated mechanics engine (lib/game/mechanics_engine.dart) that:
- Handles 10 different game mechanics that rotate every 10 levels
- Manages target systems with multiple target types
- Implements adaptive difficulty based on player performance
- Provides haptic feedback and visual effects
- Supports power-up integration
- Tracks scoring and combo systems

Target Types:
- Easy Ball: Basic targets, standard points
- Medium Ball: Moderate difficulty, higher points  
- Hard Ball: Difficult targets, highest points, can split into multiple targets
- Red Enemy: Penalty targets, negative points
- Yellow Enemy: Timer reduction targets

Power-Up System:
- Slow Motion: Reduces game speed
- Neon Bomb: Clears multiple targets
- Shield/Immune: Protection from penalties
- Magnet Touch: Attracts targets
- Time Boost: Adds extra time
- Swipe Mode: Permanent upgrade for swipe controls
- Multiball: Spawns splitting hard balls

=== PROGRESSION SYSTEM ===

- 100 core levels with 10 different mechanics
- Boss fights at level 5 and every 10 levels thereafter
- Bonus games unlocked with key system (3 keys required, 250 tokens per key)
- Level completion based on reaching target score within time limit
- Reward boxes every 3 levels with 5-second reveal mechanic
- Progressive difficulty scaling
- XP and token rewards for completion
- Level locking system requiring previous level completion

All games feature neon-themed visual effects, particle systems, haptic feedback, and sound integration for an immersive gaming experience.
